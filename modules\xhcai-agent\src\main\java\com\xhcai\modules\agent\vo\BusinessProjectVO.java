package com.xhcai.modules.agent.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 业务项目VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "业务项目VO")
public class BusinessProjectVO {

    /**
     * 项目ID
     */
    @Schema(description = "项目ID")
    private String id;

    /**
     * 项目名称
     */
    @Schema(description = "项目名称")
    private String name;

    /**
     * 项目描述
     */
    @Schema(description = "项目描述")
    private String description;

    /**
     * 项目负责人ID
     */
    @Schema(description = "项目负责人ID")
    private String ownerId;

    /**
     * 应用环境
     */
    @Schema(description = "应用环境：production-生产环境，test-测试环境，development-开发环境")
    private String environment;

    /**
     * 项目状态
     */
    @Schema(description = "项目状态：active-运行中，inactive-已停止，maintenance-维护中")
    private String status;

    /**
     * 项目图标
     */
    @Schema(description = "项目图标")
    private String icon;

    /**
     * 图标颜色
     */
    @Schema(description = "图标颜色")
    private String iconColor;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 是否删除
     */
    @Schema(description = "是否删除")
    private Integer deleted;

    /**
     * 创建人
     */
    @Schema(description = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    @Schema(description = "更新人")
    private String updateBy;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    @Schema(description = "租户ID")
    private String tenantId;

    /**
     * 项目负责人信息
     */
    @Schema(description = "项目负责人信息")
    private Object owner;

    /**
     * 团队成员列表
     */
    @Schema(description = "团队成员列表")
    private List<ProjectTeamMemberVO> teamMembers;

    /**
     * 智能体数量
     */
    @Schema(description = "智能体数量")
    private Integer agentCount;

    /**
     * 知识库数量
     */
    @Schema(description = "知识库数量")
    private Integer knowledgeCount;

    /**
     * 知识图谱数量
     */
    @Schema(description = "知识图谱数量")
    private Integer graphCount;

    /**
     * 运行中的智能体数量
     */
    @Schema(description = "运行中的智能体数量")
    private Integer agentRunningCount;

    /**
     * 已构建的知识库数量
     */
    @Schema(description = "已构建的知识库数量")
    private Integer knowledgeBuiltCount;

    /**
     * 知识图谱关系数量
     */
    @Schema(description = "知识图谱关系数量")
    private Integer graphRelationCount;

    @Schema(description = "用户名")
    private String ownerNickname;

    @Schema(description = "邮箱")
    private String ownerEmail;

    @Schema(description = "联系电话")
    private String ownerPhone;

    @Schema(description = "部门名称")
    private String ownerDeptName;
}
