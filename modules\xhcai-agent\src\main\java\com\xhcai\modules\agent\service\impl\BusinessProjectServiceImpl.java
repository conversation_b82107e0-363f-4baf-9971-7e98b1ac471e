package com.xhcai.modules.agent.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.modules.agent.dto.BusinessProjectCreateDTO;
import com.xhcai.modules.agent.dto.BusinessProjectQueryDTO;
import com.xhcai.modules.agent.dto.BusinessProjectUpdateDTO;
import com.xhcai.modules.agent.entity.BusinessProject;
import com.xhcai.modules.agent.entity.ProjectTeamMember;
import com.xhcai.modules.agent.mapper.BusinessProjectMapper;
import com.xhcai.modules.agent.mapper.ProjectTeamMemberMapper;
import com.xhcai.modules.agent.service.IBusinessProjectService;
import com.xhcai.modules.agent.vo.BusinessProjectVO;
import com.xhcai.modules.agent.vo.ProjectTeamMemberVO;
import com.xhcai.modules.system.entity.SysUser;
import com.xhcai.modules.system.service.ISysUserService;
import com.xhcai.modules.system.vo.SysUserVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务项目服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
@DS("master")
public class BusinessProjectServiceImpl extends ServiceImpl<BusinessProjectMapper, BusinessProject> implements IBusinessProjectService {

    @Autowired
    private ProjectTeamMemberMapper projectTeamMemberMapper;

    @Autowired
    private ISysUserService userService;

    @Override
    public IPage<BusinessProjectVO> getProjectPage(BusinessProjectQueryDTO queryDTO) {
        Page<BusinessProject> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        IPage<BusinessProjectVO> projectPage = baseMapper.selectProjectPageWithOwner(page, queryDTO);

        // 批量获取所有项目的团队成员信息
        List<String> projectIds = projectPage.getRecords().stream()
                .map(BusinessProjectVO::getId)
                .collect(Collectors.toList());

        Map<String, List<ProjectTeamMemberVO>> projectTeamMembersMap = batchGetProjectTeamMembers(projectIds);

        projectPage.getRecords().forEach(vo -> {
            // 设置团队成员
            vo.setTeamMembers(projectTeamMembersMap.getOrDefault(vo.getId(), new ArrayList<>()));

            // 查询统计信息
            BusinessProject stats = baseMapper.selectProjectStats(vo.getId());
            if (stats != null) {
                vo.setAgentCount(stats.getAgentCount());
                vo.setAgentRunningCount(stats.getAgentRunningCount());
                vo.setKnowledgeCount(stats.getKnowledgeCount());
                vo.setKnowledgeBuiltCount(stats.getKnowledgeBuiltCount());
                vo.setGraphCount(stats.getGraphCount());
                vo.setGraphRelationCount(stats.getGraphRelationCount());
            }
        });

        return projectPage;
    }

    @Override
    public BusinessProjectVO getProjectById(String id) {
        BusinessProject project = baseMapper.selectProjectWithOwnerById(id);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }

        BusinessProjectVO vo = convertToVO(project);

        // 查询团队成员
        Map<String, List<ProjectTeamMemberVO>> projectTeamMembersMap = batchGetProjectTeamMembers(Arrays.asList(id));
        vo.setTeamMembers(projectTeamMembersMap.getOrDefault(id, new ArrayList<>()));

        // 查询统计信息
        BusinessProject stats = baseMapper.selectProjectStats(id);
        if (stats != null) {
            vo.setAgentCount(stats.getAgentCount());
            vo.setAgentRunningCount(stats.getAgentRunningCount());
            vo.setKnowledgeCount(stats.getKnowledgeCount());
            vo.setKnowledgeBuiltCount(stats.getKnowledgeBuiltCount());
            vo.setGraphCount(stats.getGraphCount());
            vo.setGraphRelationCount(stats.getGraphRelationCount());
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProjectVO createProject(BusinessProjectCreateDTO createDTO) {
        // 检查项目名称是否重复
        LambdaQueryWrapper<BusinessProject> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BusinessProject::getName, createDTO.getName())
                .eq(BusinessProject::getDeleted, 0);
        if (baseMapper.selectCount(wrapper) > 0) {
            throw new BusinessException("项目名称已存在");
        }

        // 创建项目
        BusinessProject project = new BusinessProject();
        BeanUtils.copyProperties(createDTO, project);
        project.setStatus("active"); // 默认状态为活跃

        if (!save(project)) {
            throw new BusinessException("创建项目失败");
        }

        // 添加团队成员
        if (!CollectionUtils.isEmpty(createDTO.getTeamMembers())) {
            addTeamMembers(project.getId(), createDTO.getTeamMembers());
        }

        return getProjectById(project.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProjectVO updateProject(String id, BusinessProjectUpdateDTO updateDTO) {
        BusinessProject existingProject = getById(id);
        if (existingProject == null) {
            throw new BusinessException("项目不存在");
        }

        // 检查项目名称是否重复（排除当前项目）
        if (StringUtils.hasText(updateDTO.getName())) {
            LambdaQueryWrapper<BusinessProject> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(BusinessProject::getName, updateDTO.getName())
                    .eq(BusinessProject::getDeleted, 0)
                    .ne(BusinessProject::getId, id);
            if (baseMapper.selectCount(wrapper) > 0) {
                throw new BusinessException("项目名称已存在");
            }
        }

        // 更新项目信息
        BusinessProject project = new BusinessProject();
        BeanUtils.copyProperties(updateDTO, project);
        project.setId(id);

        if (!updateById(project)) {
            throw new BusinessException("更新项目失败");
        }

        return getProjectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProject(String id) {
        BusinessProject project = getById(id);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }

        // 删除项目（逻辑删除）
        if (!removeById(id)) {
            return false;
        }

        // 删除团队成员
        projectTeamMemberMapper.deleteByProjectId(id);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProjects(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return true;
        }

        for (String id : ids) {
            deleteProject(id);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProjectVO updateProjectTeam(String projectId, List<BusinessProjectCreateDTO.TeamMemberDTO> teamMembers) {
        BusinessProject project = getById(projectId);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }

        // 删除现有团队成员
        projectTeamMemberMapper.deleteByProjectId(projectId);

        // 添加新的团队成员
        if (!CollectionUtils.isEmpty(teamMembers)) {
            addTeamMembers(projectId, teamMembers);
        }

        return getProjectById(projectId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addTeamMember(String projectId, String userId, String role) {
        BusinessProject project = getById(projectId);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }

        // 检查用户是否已经是团队成员
        ProjectTeamMember existingMember = projectTeamMemberMapper.selectByProjectIdAndUserId(projectId, userId);
        if (existingMember != null) {
            throw new BusinessException("用户已经是团队成员");
        }

        ProjectTeamMember member = new ProjectTeamMember();
        member.setProjectId(projectId);
        member.setUserId(userId);
        member.setRole(role);
        member.setJoinTime(LocalDateTime.now());

        return projectTeamMemberMapper.insert(member) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTeamMember(String projectId, String userId) {
        return projectTeamMemberMapper.deleteByProjectIdAndUserId(projectId, userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeamMemberRole(String projectId, String userId, String role) {
        ProjectTeamMember member = projectTeamMemberMapper.selectByProjectIdAndUserId(projectId, userId);
        if (member == null) {
            throw new BusinessException("团队成员不存在");
        }

        member.setRole(role);
        return projectTeamMemberMapper.updateById(member) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BusinessProjectVO switchEnvironment(String projectId, String environment) {
        BusinessProject project = getById(projectId);
        if (project == null) {
            throw new BusinessException("项目不存在");
        }

        project.setEnvironment(environment);
        if (!updateById(project)) {
            throw new BusinessException("切换环境失败");
        }

        return getProjectById(projectId);
    }

    @Override
    public BusinessProject getProjectStats(String projectId) {
        return baseMapper.selectProjectStats(projectId);
    }

    /**
     * 添加团队成员
     */
    private void addTeamMembers(String projectId, List<BusinessProjectCreateDTO.TeamMemberDTO> teamMembers) {
        LocalDateTime now = LocalDateTime.now();
        for (BusinessProjectCreateDTO.TeamMemberDTO memberDTO : teamMembers) {
            ProjectTeamMember member = new ProjectTeamMember();
            member.setProjectId(projectId);
            member.setUserId(memberDTO.getUserId());
            member.setRole(memberDTO.getRole());
            member.setJoinTime(now);
            projectTeamMemberMapper.insert(member);
        }
    }

    /**
     * 转换为VO
     */
    private BusinessProjectVO convertToVO(BusinessProject project) {
        BusinessProjectVO vo = new BusinessProjectVO();
        BeanUtils.copyProperties(project, vo);
        return vo;
    }

    /**
     * 批量获取项目团队成员信息
     *
     * @param projectIds 项目ID列表
     * @return 项目团队成员Map，key为项目ID，value为团队成员列表
     */
    private Map<String, List<ProjectTeamMemberVO>> batchGetProjectTeamMembers(List<String> projectIds) {
        Map<String, List<ProjectTeamMemberVO>> result = new HashMap<>();

        if (CollectionUtils.isEmpty(projectIds)) {
            return result;
        }

        // 1. 批量查询所有项目的团队成员
        Map<String, List<ProjectTeamMember>> projectMembersMap = new HashMap<>();
        Set<String> allUserIds = new HashSet<>();

        for (String projectId : projectIds) {
            List<ProjectTeamMember> members = projectTeamMemberMapper.selectTeamMembersByProjectId(projectId);
            projectMembersMap.put(projectId, members);

            // 收集所有用户ID
            for (ProjectTeamMember member : members) {
                if (StringUtils.hasText(member.getUserId())) {
                    allUserIds.add(member.getUserId());
                }
            }
        }

        // 2. 批量获取用户信息
        Map<String, SysUserVO> userInfoMap = batchGetUserInfo(allUserIds);

        // 3. 组装结果
        for (String projectId : projectIds) {
            List<ProjectTeamMember> members = projectMembersMap.getOrDefault(projectId, new ArrayList<>());
            List<ProjectTeamMemberVO> memberVOs = members.stream()
                    .map(member -> convertTeamMemberToVO(member, userInfoMap))
                    .collect(Collectors.toList());
            result.put(projectId, memberVOs);
        }

        return result;
    }

    /**
     * 批量获取用户信息
     *
     * @param userIds 用户ID集合
     * @return 用户信息Map，key为用户ID，value为用户信息
     */
    private Map<String, SysUserVO> batchGetUserInfo(Set<String> userIds) {
        Map<String, SysUserVO> userInfoMap = new HashMap<>();

        if (userIds.isEmpty()) {
            return userInfoMap;
        }

        // 批量查询用户信息
        List<SysUser> users = userService.listByIds(userIds);

        for (SysUser user : users) {
            SysUserVO userVO = new SysUserVO();
            BeanUtils.copyProperties(user, userVO);
            userInfoMap.put(user.getId(), userVO);
        }

        return userInfoMap;
    }

    /**
     * 转换团队成员为VO
     */
    private ProjectTeamMemberVO convertTeamMemberToVO(ProjectTeamMember member, Map<String, SysUserVO> userInfoMap) {
        ProjectTeamMemberVO vo = new ProjectTeamMemberVO();
        BeanUtils.copyProperties(member, vo);

        // 设置用户信息
        if (StringUtils.hasText(member.getUserId())) {
            SysUserVO userVO = userInfoMap.get(member.getUserId());
            vo.setUser(userVO);
        }

        return vo;
    }
}
